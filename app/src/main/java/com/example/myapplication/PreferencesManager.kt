package com.example.myapplication

import android.content.Context
import android.content.SharedPreferences

class PreferencesManager(context: Context) {
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    companion object {
        const val PREFS_NAME = "LoginPrefs"
        const val KEY_AUTO_LOGIN = "auto_login"
        const val KEY_USERNAME = "username"
        const val KEY_PASSWORD = "password"
    }
    
    fun saveLoginInfo(username: String, password: String) {
        val editor = sharedPreferences.edit()
        editor.putBoolean(KEY_AUTO_LOGIN, true)
        editor.putString(KEY_USERNAME, username)
        editor.putString(KEY_PASSWORD, password)
        editor.apply()
    }
    
    fun isAutoLoginEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_AUTO_LOGIN, false)
    }
    
    fun getUsername(): String {
        return sharedPreferences.getString(KEY_USERNAME, "") ?: ""
    }
    
    fun clearAutoLogin() {
        val editor = sharedPreferences.edit()
        editor.putBoolean(KEY_AUTO_LOGIN, false)
        editor.apply()
    }
}
