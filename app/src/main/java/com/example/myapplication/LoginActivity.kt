package com.example.myapplication

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.CheckBox
import android.widget.EditText
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity

class LoginActivity : AppCompatActivity() {

    private lateinit var etUsername: EditText
    private lateinit var etPassword: EditText
    private lateinit var btnLogin: Button
    private lateinit var cbAutoLogin: CheckBox
    private lateinit var preferencesManager: PreferencesManager

    companion object {
        const val CORRECT_USERNAME = "123"
        const val CORRECT_PASSWORD = "abc"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login)

        initViews()
        initPreferencesManager()
        loadSavedLoginInfo()
        setupClickListeners()
    }

    private fun initViews() {
        etUsername = findViewById(R.id.et_username)
        etPassword = findViewById(R.id.et_password)
        btnLogin = findViewById(R.id.btn_login)
        cbAutoLogin = findViewById(R.id.cb_auto_login)
    }

    private fun initPreferencesManager() {
        preferencesManager = PreferencesManager(this)
    }

    private fun loadSavedLoginInfo() {
        // 如果之前保存过登录信息，自动填充用户名并勾选自动登录
        if (preferencesManager.isAutoLoginEnabled()) {
            val savedUsername = preferencesManager.getUsername()
            etUsername.setText(savedUsername)
            cbAutoLogin.isChecked = true
        }
    }

    private fun setupClickListeners() {
        btnLogin.setOnClickListener {
            val username = etUsername.text.toString().trim()
            val password = etPassword.text.toString().trim()

            if (validateLogin(username, password)) {
                // 只有勾选了自动登录才保存登录信息
                if (cbAutoLogin.isChecked) {
                    saveLoginInfo(username, password)
                } else {
                    // 如果没有勾选自动登录，清除之前的自动登录设置
                    preferencesManager.clearAutoLogin()
                }
                navigateToUserInfo(username)
            } else {
                showErrorMessage()
            }
        }
    }

    private fun validateLogin(username: String, password: String): Boolean {
        return username == CORRECT_USERNAME && password == CORRECT_PASSWORD
    }

    private fun saveLoginInfo(username: String, password: String) {
        preferencesManager.saveLoginInfo(username, password)
    }

    private fun navigateToUserInfo(username: String) {
        val intent = Intent(this, UserInfoActivity::class.java)
        intent.putExtra("username", username)
        startActivity(intent)
        finish()
    }

    private fun showErrorMessage() {
        Toast.makeText(this, "用户名或密码错误", Toast.LENGTH_SHORT).show()
    }
}
