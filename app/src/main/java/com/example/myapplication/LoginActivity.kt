package com.example.myapplication

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity

class LoginActivity : AppCompatActivity() {

    private lateinit var etUsername: EditText
    private lateinit var etPassword: EditText
    private lateinit var btnLogin: Button

    companion object {
        const val CORRECT_USERNAME = "123"
        const val CORRECT_PASSWORD = "abc"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login)

        initViews()
        setupClickListeners()
    }

    private fun initViews() {
        etUsername = findViewById(R.id.et_username)
        etPassword = findViewById(R.id.et_password)
        btnLogin = findViewById(R.id.btn_login)
    }

    private fun setupClickListeners() {
        btnLogin.setOnClickListener {
            val username = etUsername.text.toString().trim()
            val password = etPassword.text.toString().trim()

            if (validateLogin(username, password)) {
                navigateToUserInfo(username)
            } else {
                showErrorMessage()
            }
        }
    }

    private fun validateLogin(username: String, password: String): Boolean {
        return username == CORRECT_USERNAME && password == CORRECT_PASSWORD
    }

    private fun navigateToUserInfo(username: String) {
        val intent = Intent(this, UserInfoActivity::class.java)
        intent.putExtra("username", username)
        startActivity(intent)
        finish()
    }

    private fun showErrorMessage() {
        Toast.makeText(this, "用户名或密码错误", Toast.LENGTH_SHORT).show()
    }
}
