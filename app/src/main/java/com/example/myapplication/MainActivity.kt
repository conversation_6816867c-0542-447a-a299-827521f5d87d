package com.example.myapplication

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity

class MainActivity : AppCompatActivity() {

    private lateinit var preferencesManager: PreferencesManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        initPreferencesManager()
        checkAutoLogin()
    }

    private fun initPreferencesManager() {
        preferencesManager = PreferencesManager(this)
    }

    private fun checkAutoLogin() {
        if (preferencesManager.isAutoLoginEnabled()) {
            val username = preferencesManager.getUsername()
            navigateToUserInfo(username)
        } else {
            navigateToLogin()
        }
    }

    private fun navigateToUserInfo(username: String) {
        val intent = Intent(this, UserInfoActivity::class.java)
        intent.putExtra("username", username)
        startActivity(intent)
        finish()
    }

    private fun navigateToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        finish()
    }
}

