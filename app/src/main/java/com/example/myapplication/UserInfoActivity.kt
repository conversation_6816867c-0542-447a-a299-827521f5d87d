package com.example.myapplication

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity

class UserInfoActivity : AppCompatActivity() {
    
    private lateinit var tvUsername: TextView
    private lateinit var btnLogout: Button
    private lateinit var preferencesManager: PreferencesManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_user_info)
        
        initViews()
        initPreferencesManager()
        displayUserInfo()
        setupClickListeners()
    }

    private fun initViews() {
        tvUsername = findViewById(R.id.tv_username)
        btnLogout = findViewById(R.id.btn_logout)
    }

    private fun initPreferencesManager() {
        preferencesManager = PreferencesManager(this)
    }
    
    private fun displayUserInfo() {
        val username = intent.getStringExtra("username")
            ?: preferencesManager.getUsername()

        tvUsername.text = "欢迎，$username"
    }
    
    private fun setupClickListeners() {
        btnLogout.setOnClickListener {
            logout()
        }
    }
    
    private fun logout() {
        // Clear auto-login flag
        preferencesManager.clearAutoLogin()

        // Navigate back to login
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
}
