<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="32dp"
    android:gravity="center">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="用户信息"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="48dp"
        android:textColor="@android:color/black" />

    <TextView
        android:id="@+id/tv_username"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="欢迎，用户"
        android:textSize="18sp"
        android:layout_marginBottom="32dp"
        android:textColor="@android:color/black" />

    <Button
        android:id="@+id/btn_logout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="退出登录"
        android:textSize="16sp"
        android:padding="12dp" />

</LinearLayout>
